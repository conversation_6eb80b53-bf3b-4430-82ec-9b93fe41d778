# The-Solar-System
We created a fully interactive 3D model of our Solar System using JavaScript and the three.min.js library. Watch as we render all 8 planets, and even major moons like Europa and Titan, realistic scales, and dynamic lighting.


# Run it
To run this code you will need an http server, for instance you can use Python for this purpose:

	1) move to your folder and execute
		C:\Python37\python.exe -m http.server
	2) In the browser enter the page you would like to execute
		http://localhost:8000/[YourFile].html

