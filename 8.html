<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Planet Viewer with Logarithmic Zoom</title>
    <style>
        body { margin: 0; overflow: hidden; }
        canvas { display: block; }

        /* Menu styling */      
        .menu-container {
            position: absolute;
            top: 20px;
            right: 10px;
            display: flex;
            gap: 10px;
        }
        .menu-column {
            display: flex;
            flex-direction: column;
            gap: 5px;
        }
        button {
			text-align: left;
            padding: 7px 5px;
            background-color: #444444;
            color: #aaaaee;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            min-width: 100px;
            transition: background-color 0.2s;
        }
        button:hover {
            background-color: #888888;
        }
        button:active {
            background-color: #1d1d1d;
        }

		
		
		#controls {
            position: absolute;
            bottom: 20px;
            right: 150px;
            z-index: 100;
			background-color: #444444;
            padding: 10px;
            border-radius: 8px;
            font-family: Arial, sans-serif;
			font-size: 14px;
            box-shadow: 0 0 10px rgba(0,0,0,0.2);
        }
		
		label {
            display: inline-block;
            width: 130px;
            color: #aaaaee;
        }
		
        .slider-container {
            margin: 10px 0;
        }
        
		input[type="range"] {
            width: 250px;
            vertical-align: middle;
        }
        .value-display {
            display: inline-block;
			color: #aaaaee;
            width: 60px;
            text-align: right;
            margin-left: 5px;
        }
    </style>
</head>
<body>
    <div id="controls">

        <div class="slider-container">
            <label for="zoomControl">Zoom (log):</label>
            <input type="range" id="zoomControl" min="0" max="4.5" step="0.01" value="1">
            <span class="value-display" id="zoomValue">1.3</span>
        </div>
	
        <div class="slider-container">
            <label for="ambientLight">Ambient Light:</label>
            <input type="range" id="ambientLight" min="0" max="2" step="0.01" value="0.4">
            <span class="value-display" id="ambientValue">0.4</span>
        </div>
        
        <div class="slider-container">
            <label for="directionalLight">Directional Light:</label>
            <input type="range" id="directionalLight" min="0" max="3" step="0.1" value="1">
            <span class="value-display" id="directionalValue">1.0</span>
        </div>
        
    </div>
	
	
	<div class="menu-container">
        <div class="menu-column">
		
			<h3 style="color:white; margin-top:0;">Planets:</h3>
			
			<button id="PMer">Mercury (0.38)</button>
			<button id="PV">Venus (0.95)</button>
			<button id="PEsat">Earth</button>
			<button id="PEpol">Earth Politic</button>
		
			<button id="PMar">Mars (0.53)</button>
			<button id="PJ">Jupiter (11.2)</button>
			<button id="PS">Saturn (9.45)</button>
			<button id="PU">Uranus (4.01)</button>
			<button id="PN">Neptune (3.88)</button>
			<button id="PP">Pluto (0.186)</button>
			<button id="PC">Coruscant</button>

			
        </div>
        <div class="menu-column">
		
			<h3 style="color:white; margin-top:0;">Earth 1:</h3>
			<button id="ME">Moon (0.27)</button>
			
			<h3 style="color:white; margin-top:0;">Mars 2:</h3>
			<button id="MMP">Phobos {0.00176}</button>
			<button id="MMD">Deimos {0.00097}</button>

			

			
			<h3 style="color:white; margin-top:0;">Jupiter 95:</h3>
			<button id="MJG">Ganymede (0.41)</button>
			<button id="MJC">Callisto (0.38)</button>
			<button id="MJI">Io (0.29)</button>
			<button id="MJE">Europa (0.25)</button>

			
			
			<h3 style="color:white; margin-top:0;">Saturn 146:</h3>
			<button id="MSTi">Titan (0.41)</button>
			<button id="MSI">Iapetus (0.115)</button>
			<button id="MSR">Rhea {0.12}</button>
			<button id="MSTe">Tethys (0.083)</button>
			<button id="MSD">Dione (0.088)</button>
			<button id="MSE">Enceladus (0.04)</button>
			<button id="MSM">Mimas ( 0.031)</button>
			<button id="MSH">Hyperion (0.021)</button>

			<h3 style="color:white; margin-top:0;">Uranus 28:</h3>
			<button id="MUT">Titania (0.124)</button>
			<button id="MUO">Oberon (0.12)</button>
			<button id="MUM">Miranda (0.037)</button>
			
			<h3 style="color:white; margin-top:0;">Neptune 16:</h3>
			<button id="MNT">Triton (0.21)</button>

			
			<br>
        </div>
    </div>
	
	
	
    <script src="./JS/three.min.js"></script>
    <script>
        // Global variables
        let scene, camera, renderer;
        let planet, clouds;
        let ambientLight, directionalLight;
        let isDragging = false;
        let previousMousePosition = { x: 0, y: 0 };
        let rotationSpeed = 0.002;
        
        // Logarithmic zoom conversion
        function logToLinear(value) {
            return Math.pow(10, value); // 10^value
        }
        
        function linearToLog(value) {
            return Math.log10(value); // log10(value)
        }
        
		
		
		
		
		
		
		
		
		
		
        //=========================== Initialize the scene
        function init() {
            //-------- Create scene
            scene = new THREE.Scene();
            
            // Create camera with large depth range
            camera = new THREE.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 0.1, 100000);
            camera.position.z = 10;
            
            //-------- Create renderer
            renderer = new THREE.WebGLRenderer({ antialias: true });
            renderer.setSize(window.innerWidth, window.innerHeight);
            document.body.appendChild(renderer.domElement);
            
            //-------- Add lights
            ambientLight = new THREE.AmbientLight(0x404040);
            scene.add(ambientLight);
            
            directionalLight = new THREE.DirectionalLight(0xffffff, 1);
            directionalLight.position.set(-1, 1, 1);
            scene.add(directionalLight);
            
            //-------- Create default planet (Planet 1)
			createPlanet('./pic/Earth.jpg', './pic/Earth_Clouds.png', 1*1000);
 
            //-------- Add event listeners
            renderer.domElement.addEventListener('mousedown', onMouseDown);
            renderer.domElement.addEventListener('mousemove', onMouseMove);
            renderer.domElement.addEventListener('mouseup', onMouseUp);
            renderer.domElement.addEventListener('mouseleave', onMouseUp);
            window.addEventListener('wheel', onMouseWheel, { passive: false });
            window.addEventListener('resize', onWindowResize);
            

            //-------- Button event listeners
			//document.getElementById('PMer').addEventListener('click', () => {createPlanet('./pic/Mercury.webp', null, 0.38*1000);});
			document.getElementById('PMer').addEventListener('click', () => {createPlanet('./pic/Mercury3.jpg', null, 0.38*1000);});
			document.getElementById('PV').addEventListener('click', () => {createPlanet('./pic/Venus.webp', './pic/Venus_Clouds.webp', 0.95*1000);});
            document.getElementById('PEsat').addEventListener('click', () => {createPlanet('./pic/Earth_Sat.jpg', './pic/Earth_Clouds.png', 1.0*1000);});     document.getElementById('PEpol').addEventListener('click', () => {createPlanet('./pic/Earth_Politic.jpg', './pic/Earth_Clouds.png', 1.0*1000);});
			document.getElementById('PMar').addEventListener('click', () => {createPlanet('./pic/Mars.jpg', null, 0.53*1000);});
			//document.getElementById('PJ').addEventListener('click', () => {createPlanet('./pic/Jupiter.webp', null, 11.21*1000);});
			document.getElementById('PJ').addEventListener('click', () => {createPlanet('./pic/Jupiter3.jpg', null, 11.21*1000);});
			document.getElementById('PS').addEventListener('click', () => {createPlanet('./pic/Saturn.webp', null, 9.45*1000);});
			document.getElementById('PU').addEventListener('click', () => {createPlanet('./pic/Uranus.webp', null, 4.01*1000);});
			document.getElementById('PN').addEventListener('click', () => {createPlanet('./pic/Neptune.webp', null, 3.88*1000);});
			document.getElementById('PP').addEventListener('click', () => {createPlanet('./pic/Pluto.webp', null, 0.186*1000);});
			
			document.getElementById('PC').addEventListener('click', () => {createPlanet('./pic/Coruscant.jpg', './pic/Earth_Clouds.png', 0.7*1000);});
			
			

			document.getElementById('ME').addEventListener('click', () => {createPlanet('./pic/Moon.jpg', null, 0.27*1000);});
			
			document.getElementById('MMP').addEventListener('click', () => {createPlanet('./pic/Mars_Phobos.webp', null, 0.00176*1000);});
			document.getElementById('MMD').addEventListener('click', () => {createPlanet('./pic/Mars_Deimos.webp', null, 0.00097*1000);});
			
			document.getElementById('MJG').addEventListener('click', () => {createPlanet('./pic/Jupiter_Ganymede.webp', null, 0.41*1000);});
			document.getElementById('MJC').addEventListener('click', () => {createPlanet('./pic/Jupiter_Callisto.webp', null, 0.38*1000);});
			document.getElementById('MJI').addEventListener('click', () => {createPlanet('./pic/Jupiter_Io.webp', null, 0.29*1000);});
			document.getElementById('MJE').addEventListener('click', () => {createPlanet('./pic/Jupiter_Europa.webp', null, 0.25*1000);});
			
			document.getElementById('MSTi').addEventListener('click', () => {createPlanet('./pic/Saturn_Titan.webp', './pic/Saturn_Titan_Clauds.webp', 0.41*1000);});
			document.getElementById('MSI').addEventListener('click', () => {createPlanet('./pic/Saturn_Iapetus.webp', null, 0.115*1000);});
			document.getElementById('MSR').addEventListener('click', () => {createPlanet('./pic/Saturn_Rhea.webp', null, 0.12*1000);});
			document.getElementById('MSTe').addEventListener('click', () => {createPlanet('./pic/Saturn_Tethys.webp', null, 0.083*1000);});
			document.getElementById('MSD').addEventListener('click', () => {createPlanet('./pic/Saturn_Dione.webp', null, 0.088*1000);});
			document.getElementById('MSE').addEventListener('click', () => {createPlanet('./pic/Saturn_Enceladus.webp', null, 0.04*1000);});
			document.getElementById('MSM').addEventListener('click', () => {createPlanet('./pic/Saturn_Mimas.webp', null, 0.031*1000);});
			document.getElementById('MSH').addEventListener('click', () => {createPlanet('./pic/Saturn_Hyperion.webp', null, 0.021*1000);});
			
			document.getElementById('MUT').addEventListener('click', () => {createPlanet('./pic/Uranus_Titania.webp', null, 0.124*1000);});
			document.getElementById('MUO').addEventListener('click', () => {createPlanet('./pic/Uranus_Oberon.webp', null, 0.12*1000);});
			document.getElementById('MUM').addEventListener('click', () => {createPlanet('./pic/Uranus_Miranda.webp', null, 0.037*1000);});
			
			document.getElementById('MNT').addEventListener('click', () => {createPlanet('./pic/Neptun_Triton.webp', null, 0.212*1000);});
			
			
			
			
            
            //-------- Slider event listeners
            document.getElementById('ambientLight').addEventListener('input', updateAmbientLight);
            document.getElementById('directionalLight').addEventListener('input', updateDirectionalLight);
            document.getElementById('zoomControl').addEventListener('input', updateZoom);


            
            //-------- Initialize slider values
            updateAmbientLight();
            updateDirectionalLight();
            updateZoom();
            
            // Start animation loop
            animate();
        }
        
		
		
		
		
		
		
		
		
		
		
        //===============  Function to create planet with specific parameters (sizes multiplied by 1000)
        function createPlanet(surfaceTexturePath, cloudTexturePath, diameter) {
            // Remove existing planet if any
            if (planet) scene.remove(planet);
            if (clouds) scene.remove(clouds);
            
            // Create planet surface (sizes are 1000x original)
            const surfaceTexture = new THREE.TextureLoader().load(surfaceTexturePath);
            const surfaceGeometry = new THREE.SphereGeometry(diameter/2, 64, 64);
            const surfaceMaterial = new THREE.MeshPhongMaterial({ 
                map: surfaceTexture,
                bumpScale: 0.05 * (diameter/1000) // Scale bump effect with original size
            });
            planet = new THREE.Mesh(surfaceGeometry, surfaceMaterial);
            scene.add(planet);
            
            // Only create cloud layer if cloud texture path is provided
            if (cloudTexturePath) {
                const cloudTexture = new THREE.TextureLoader().load(cloudTexturePath, undefined, 
                    function(err) {
                        console.error('Could not load cloud texture:', err);
                        if (clouds) {
                            scene.remove(clouds);
                            clouds = null;
                        }
                    });
                
                const cloudGeometry = new THREE.SphereGeometry((diameter/2) * 1.04, 64, 64);
                const cloudMaterial = new THREE.MeshPhongMaterial({ 
                    map: cloudTexture,
                    transparent: true,
                    opacity: 0.7
                });
                clouds = new THREE.Mesh(cloudGeometry, cloudMaterial);
                scene.add(clouds);
            } else {
                clouds = null;
            }
        }
        
        
        // Update ambient light intensity
        function updateAmbientLight() {
            const value = parseFloat(document.getElementById('ambientLight').value);
            ambientLight.intensity = value;
            document.getElementById('ambientValue').textContent = value.toFixed(2);
        }
        
        // Update directional light intensity
        function updateDirectionalLight() {
            const value = parseFloat(document.getElementById('directionalLight').value);
            directionalLight.intensity = value;
            document.getElementById('directionalValue').textContent = value.toFixed(1);
        }
        
        // Update camera zoom (logarithmic)
        function updateZoom() {
            const logValue = parseFloat(document.getElementById('zoomControl').value);
            const linearValue = logToLinear(logValue);
            camera.position.z = linearValue;
            document.getElementById('zoomValue').textContent = linearValue.toFixed(1);
        }
        
        // Mouse wheel handler for zoom (logarithmic)
        function onMouseWheel(event) {
            event.preventDefault();
            
            // Get current logarithmic zoom value
            const currentLogZoom = linearToLog(camera.position.z);
            
			const zoomDelta = event.deltaY * 0.0002;
            
            // Apply zoom in logarithmic space
            const newLogZoom = currentLogZoom + zoomDelta;
            
            // Convert back to linear space and apply
            const newLinearZoom = logToLinear(newLogZoom);
            camera.position.z = Math.max(0.1, Math.min(20000, newLinearZoom));
            
            // Update UI
            document.getElementById('zoomControl').value = linearToLog(camera.position.z);
            document.getElementById('zoomValue').textContent = camera.position.z.toFixed(1)/1000;
        }
        
        // Animation loop
        function animate() {
            requestAnimationFrame(animate);
            
            // Rotate planet and clouds at different speeds
            if (planet) planet.rotation.y += rotationSpeed * 2;
            if (clouds) clouds.rotation.y += rotationSpeed * 2.9;
            
            renderer.render(scene, camera);
        }
        
        // Mouse event handlers
        function onMouseDown(event) {
            isDragging = true;
            previousMousePosition = {
                x: event.clientX,
                y: event.clientY
            };
        }
        
        function onMouseMove(event) {
            if (!isDragging) return;
            
            const deltaMove = {
                x: event.clientX - previousMousePosition.x,
                y: event.clientY - previousMousePosition.y
            };
            
            if (planet) {
                planet.rotation.y += deltaMove.x * 0.01;
                planet.rotation.x += deltaMove.y * 0.01;
            }
            
            if (clouds) {
                clouds.rotation.y += deltaMove.x * 0.01;
                clouds.rotation.x += deltaMove.y * 0.01;
            }
            
            previousMousePosition = {
                x: event.clientX,
                y: event.clientY
            };
        }
        
        function onMouseUp() {
            isDragging = false;
        }
        
        // Handle window resize
        function onWindowResize() {
            camera.aspect = window.innerWidth / window.innerHeight;
            camera.updateProjectionMatrix();
            renderer.setSize(window.innerWidth, window.innerHeight);
        }
        
        // Start the application
        init();
    </script>
</body>
</html>